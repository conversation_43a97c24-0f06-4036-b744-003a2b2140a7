
# 时间验证逻辑重构：从CDN依赖迁移到自定义API端点

## TODO

修改时间验证逻辑
目标：将 AnonymousPurchaseService 类中的 validateSystemTime() 方法，从依赖 CDN 文件时间戳，修改为依赖自定义后端 API 端点返回的时间戳。

修改内容：

API 端点：将网络请求的 URL 从 https://cdn.senseword.app/api/v1/config/version.json 更新为后端接口

数据解析：

移除对 HTTP 响应头 Date 字段的解析逻辑。

添加 JSON 解析代码，以处理后端返回的 JSON 响应体。

期望的 JSON 格式：{"timestamp": 1753670400.0}。

将解析出的 timestamp 值（一个 Unix 时间戳，类型为 TimeInterval 或 Double）转换为 Date 对象。

时间比对：

保留将服务器时间与设备当前时间 Date() 进行比对的逻辑。

时间差的容忍度仍为 300 秒（5分钟）。

错误处理：

保留 do-catch 块。

当网络请求失败或 JSON 解析失败时，在 catch 块中记录日志。

确保在任何错误情况下，validateSystemTime() 方法都返回 true，以保持应用的容错性和可用性。

注意前端的网络能力需要通过 adapter 层来接入，而不是直接在 service 层创建网络连接

## AI 分析区域

### 关键发现和根本原因分析

1. **当前实现分析**：
   - 当前 `validateSystemTime()` 方法位于 `iOS/SensewordApp/Services/AnonymousPurchaseService.swift` 第273-302行
   - 使用直接的 `URLSession.shared.data(from: url)` 调用，违反了架构分层原则
   - 依赖 CDN 的 HTTP 响应头 `Date` 字段，存在可靠性风险
   - 当前端点：`https://cdn.senseword.app/api/v1/config/version.json`

2. **架构问题诊断**：
   - **违反分层原则**：Service 层直接进行网络调用，应该通过 Adapter 层
   - **依赖外部CDN**：CDN 时间戳可能不够精确或可靠
   - **缺少专用时间API**：当前没有专门的时间同步端点

3. **技术挑战识别**：
   - 需要创建新的时间同步 API Adapter
   - 需要定义时间戳响应的数据模型
   - 需要确保网络异常时的容错性
   - 需要保持现有的5分钟容忍度逻辑

### 解决方案设计思路

1. **API端点设计**：
   - 新端点：`/api/v1/system/timestamp`
   - 响应格式：`{"timestamp": 1753670400.0, "timezone": "UTC"}`
   - 使用静态API密钥认证

2. **架构重构方案**：
   - 创建 `SystemAPIAdapter` 处理系统级API调用
   - 定义 `TimestampResponse` 数据模型
   - 修改 `AnonymousPurchaseService` 使用 Adapter 层
   - 保持现有的容错机制

3. **实现策略**：
   - 分阶段实施：先创建 Adapter，再修改 Service
   - 保持向后兼容：确保错误时返回 true
   - 增强日志记录：便于调试和监控

## CML 任务清单

### 阶段一：创建后端时间戳API端点
- [ ] 在主API服务器中创建 `/api/v1/system/timestamp` 端点：返回当前服务器时间戳的JSON响应
- [ ] 实现端点逻辑：返回格式 `{"timestamp": Date.now() / 1000, "timezone": "UTC"}` 的JSON响应
- [ ] 配置端点认证：使用静态API密钥认证，与其他公开端点保持一致
- [ ] 添加端点文档：在API文档中记录新的时间戳端点规范

### 阶段二：创建iOS系统时间API基础设施
- [ ] 在 `iOS/SensewordApp/Models/Responses/` 目录下创建 `TimestampResponse.swift` 文件：定义时间戳响应数据模型，包含 `timestamp: TimeInterval` 和可选的 `timezone: String` 字段
- [ ] 在 `iOS/SensewordApp/Services/Adapters/` 目录下创建 `SystemAPIAdapter.swift` 文件：定义 `SystemAPIAdapterProtocol` 协议和 `SystemAPIAdapter` 实现类
- [ ] 在 `SystemAPIAdapter.swift` 中实现 `getServerTimestamp()` 方法：调用 `/api/v1/system/timestamp` 端点，返回 `TimestampResponse` 对象

### 阶段三：集成系统时间Adapter到依赖注入容器
- [ ] 在 `iOS/SensewordApp/DI/AdapterContainer.swift` 文件中添加 `systemAPIAdapter` 属性：类型为 `SystemAPIAdapterProtocol`，使用懒加载初始化
- [ ] 在同一文件中实现 `systemAPIAdapter` 的懒加载逻辑：`SystemAPIAdapter(apiClient: mainAPIClient)`

### 阶段四：重构AnonymousPurchaseService时间验证逻辑
- [ ] 在 `iOS/SensewordApp/Services/AnonymousPurchaseService.swift` 文件顶部添加依赖注入：添加 `private let systemAPIAdapter: SystemAPIAdapterProtocol` 属性
- [ ] 修改 `AnonymousPurchaseService` 初始化方法：通过构造函数注入 `systemAPIAdapter` 依赖
- [ ] 重构 `validateSystemTime()` 方法第275-295行：移除直接的 URLSession 调用，改为调用 `systemAPIAdapter.getServerTimestamp()`
- [ ] 在同一方法中修改时间解析逻辑：将 `response.timestamp` 转换为 `Date(timeIntervalSince1970: response.timestamp)`，保持300秒容忍度检查
- [ ] 更新错误处理和日志记录：适配新的API调用方式，确保网络异常时仍返回 true

### 阶段五：更新依赖注入配置
- [ ] 修改创建 `AnonymousPurchaseService` 实例的代码：传入 `AdapterContainer.shared.systemAPIAdapter` 参数
- [ ] 验证单例模式兼容性：确保依赖注入不影响 `AnonymousPurchaseService.shared` 的使用

## 提交消息区域

```
feat(time-validation): 重构时间验证逻辑使用专用API端点

- 创建/api/v1/system/timestamp后端端点提供精确时间戳
- 新增TimestampResponse数据模型支持JSON时间戳解析
- 创建SystemAPIAdapter处理系统级API调用
- 重构AnonymousPurchaseService移除直接网络调用依赖
- 集成Adapter层到依赖注入容器
- 保持300秒容忍度和容错机制不变
- 使用静态API密钥认证确保安全性

BREAKING CHANGE: AnonymousPurchaseService构造函数现在需要systemAPIAdapter参数
```