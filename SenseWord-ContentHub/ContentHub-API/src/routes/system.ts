/**
 * System 路由
 * 提供系统级别的API端点，如时间戳同步等
 */
import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import type { ApiResponse } from '../types/index.js';

// 时间戳响应类型定义
interface TimestampResponse {
  timestamp: number;
  timezone: string;
}

export default async function systemRoutes(fastify: FastifyInstance) {
  // 获取服务器时间戳
  fastify.get(
    '/timestamp',
    {
      schema: {
        description: '获取服务器当前时间戳',
        tags: ['system'],
        response: {
          200: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'object',
                properties: {
                  timestamp: { type: 'number', description: 'Unix时间戳（秒）' },
                  timezone: { type: 'string', description: '时区信息' }
                }
              }
            }
          }
        }
      }
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // 获取当前时间戳（秒级）
        const timestamp = Math.floor(Date.now() / 1000);
        
        const timestampData: TimestampResponse = {
          timestamp,
          timezone: 'UTC'
        };

        const response: ApiResponse<TimestampResponse> = {
          success: true,
          data: timestampData
        };

        fastify.log.info(`[System API] 时间戳请求 - timestamp: ${timestamp}`);
        return reply.code(200).send(response);
        
      } catch (error: any) {
        fastify.log.error('获取时间戳失败:', error);
        
        const response: ApiResponse = {
          success: false,
          error: error.message || '获取时间戳失败'
        };

        return reply.code(500).send(response);
      }
    }
  );
}
